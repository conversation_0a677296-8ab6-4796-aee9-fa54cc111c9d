"use client";

import { 
  ApiQueryParams, 
  PaginatedResponse, 
  DataFetchOptions,
  LoadingState 
} from '@/lib/types';
import { CacheManager, globalCache, staticDataCache, dynamicDataCache } from '@/lib/cache-manager';
import { apiClient, handleApiResponse } from '@/lib/api-client';

/**
 * نظام جلب البيانات عند الطلب مع دعم التخزين المؤقت
 */
export class DataFetcher {
  private loadingStates = new Map<string, LoadingState>();

  /**
   * جلب البيانات مع دعم الترقيم والتصفية والتخزين المؤقت
   */
  async fetchData<T>(
    endpoint: string,
    queryParams: ApiQueryParams = {},
    options: DataFetchOptions = {}
  ): Promise<PaginatedResponse<T>> {
    const cacheKey = options.cacheKey || CacheManager.createKey(endpoint, queryParams);
    const cache = this.selectCache(endpoint);
    
    // تحديث حالة التحميل
    this.setLoadingState(cacheKey, { 
      isLoading: true, 
      isRefreshing: false, 
      error: null,
      lastFetch: null 
    });

    try {
      // فحص التخزين المؤقت إذا لم يكن مطلوب تحديث قسري
      if (options.cache !== false && !options.forceRefresh) {
        const cachedData = cache.get<PaginatedResponse<T>>(cacheKey);
        
        if (cachedData && !cache.isStale(cacheKey)) {
          this.setLoadingState(cacheKey, { 
            isLoading: false, 
            isRefreshing: false, 
            error: null,
            lastFetch: Date.now()
          });
          return cachedData;
        }

        // إذا كانت البيانات منتهية الصلاحية ولكن متاحة، أرجعها واجلب جديدة في الخلفية
        if (cachedData && cache.isStale(cacheKey)) {
          this.setLoadingState(cacheKey, { 
            isLoading: false, 
            isRefreshing: true, 
            error: null,
            lastFetch: Date.now()
          });
          
          // جلب البيانات الجديدة في الخلفية
          this.fetchInBackground(endpoint, queryParams, options, cacheKey, cache);
          return cachedData;
        }
      }

      // بناء URL مع المعاملات
      const url = this.buildUrl(endpoint, queryParams);
      
      // جلب البيانات من الخادم
      const response = await apiClient.get(url);
      const data = await handleApiResponse(response);

      // تحويل البيانات إلى تنسيق مرقم إذا لم تكن كذلك
      const paginatedData = this.ensurePaginatedFormat<T>(data, queryParams);

      // حفظ في التخزين المؤقت
      if (options.cache !== false) {
        const ttl = options.cacheTtl || this.getDefaultTtl(endpoint);
        cache.set(cacheKey, paginatedData, ttl);
      }

      this.setLoadingState(cacheKey, { 
        isLoading: false, 
        isRefreshing: false, 
        error: null,
        lastFetch: Date.now()
      });

      return paginatedData;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'خطأ في جلب البيانات';
      
      this.setLoadingState(cacheKey, { 
        isLoading: false, 
        isRefreshing: false, 
        error: errorMessage,
        lastFetch: null
      });

      // إذا كان هناك بيانات مخزنة مؤقتاً، أرجعها مع الخطأ
      const cachedData = cache.get<PaginatedResponse<T>>(cacheKey);
      if (cachedData) {
        return cachedData;
      }

      throw error;
    }
  }

  /**
   * جلب البيانات في الخلفية
   */
  private async fetchInBackground<T>(
    endpoint: string,
    queryParams: ApiQueryParams,
    options: DataFetchOptions,
    cacheKey: string,
    cache: CacheManager
  ): Promise<void> {
    try {
      const url = this.buildUrl(endpoint, queryParams);
      const response = await apiClient.get(url);
      const data = await handleApiResponse(response);
      const paginatedData = this.ensurePaginatedFormat<T>(data, queryParams);

      const ttl = options.cacheTtl || this.getDefaultTtl(endpoint);
      cache.set(cacheKey, paginatedData, ttl);

      this.setLoadingState(cacheKey, { 
        isLoading: false, 
        isRefreshing: false, 
        error: null,
        lastFetch: Date.now()
      });

    } catch (error) {
      console.error('خطأ في جلب البيانات في الخلفية:', error);
      this.setLoadingState(cacheKey, { 
        isLoading: false, 
        isRefreshing: false, 
        error: error instanceof Error ? error.message : 'خطأ في التحديث',
        lastFetch: null
      });
    }
  }

  /**
   * بناء URL مع المعاملات
   */
  private buildUrl(endpoint: string, queryParams: ApiQueryParams): string {
    const url = new URL(endpoint, window.location.origin);
    
    // إضافة معاملات الترقيم
    if (queryParams.pagination) {
      const { page, limit, offset, cursor } = queryParams.pagination;
      if (page !== undefined) url.searchParams.set('page', page.toString());
      if (limit !== undefined) url.searchParams.set('limit', limit.toString());
      if (offset !== undefined) url.searchParams.set('offset', offset.toString());
      if (cursor) url.searchParams.set('cursor', cursor);
    }

    // إضافة معاملات الفرز
    if (queryParams.sort) {
      url.searchParams.set('sortBy', queryParams.sort.field);
      url.searchParams.set('sortOrder', queryParams.sort.direction);
    }

    // إضافة معاملات التصفية
    if (queryParams.filters) {
      Object.entries(queryParams.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          url.searchParams.set(key, value.toString());
        }
      });
    }

    // إضافة معاملات البحث
    if (queryParams.search) {
      if (queryParams.search.query) {
        url.searchParams.set('search', queryParams.search.query);
      }
      if (queryParams.search.fields) {
        url.searchParams.set('searchFields', queryParams.search.fields.join(','));
      }
    }

    return url.toString();
  }

  /**
   * ضمان تنسيق البيانات المرقمة
   */
  private ensurePaginatedFormat<T>(data: any, queryParams: ApiQueryParams): PaginatedResponse<T> {
    // إذا كانت البيانات مرقمة بالفعل
    if (data && typeof data === 'object' && 'data' in data && 'pagination' in data) {
      return data as PaginatedResponse<T>;
    }

    // إذا كانت البيانات مصفوفة، قم بتحويلها إلى تنسيق مرقم
    if (Array.isArray(data)) {
      const page = queryParams.pagination?.page || 1;
      const limit = queryParams.pagination?.limit || data.length;
      const total = data.length;
      const totalPages = Math.ceil(total / limit);

      return {
        data: data as T[],
        pagination: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        meta: {
          filters: queryParams.filters,
          search: queryParams.search,
          sort: queryParams.sort
        }
      };
    }

    // تنسيق افتراضي
    return {
      data: [data] as T[],
      pagination: {
        page: 1,
        limit: 1,
        total: 1,
        totalPages: 1,
        hasNext: false,
        hasPrev: false
      }
    };
  }

  /**
   * اختيار التخزين المؤقت المناسب
   */
  private selectCache(endpoint: string): CacheManager {
    // البيانات الثابتة
    if (endpoint.includes('/users') || 
        endpoint.includes('/warehouses') || 
        endpoint.includes('/settings') ||
        endpoint.includes('/clients') ||
        endpoint.includes('/suppliers')) {
      return staticDataCache;
    }

    // البيانات الديناميكية
    if (endpoint.includes('/devices') || 
        endpoint.includes('/sales') || 
        endpoint.includes('/returns') ||
        endpoint.includes('/supply')) {
      return dynamicDataCache;
    }

    return globalCache;
  }

  /**
   * الحصول على TTL الافتراضي حسب نوع البيانات
   */
  private getDefaultTtl(endpoint: string): number {
    // البيانات الثابتة - 30 دقيقة
    if (endpoint.includes('/users') || 
        endpoint.includes('/warehouses') || 
        endpoint.includes('/settings')) {
      return 30 * 60 * 1000;
    }

    // البيانات شبه الثابتة - 10 دقائق
    if (endpoint.includes('/clients') || 
        endpoint.includes('/suppliers') ||
        endpoint.includes('/device-models')) {
      return 10 * 60 * 1000;
    }

    // البيانات الديناميكية - 2 دقيقة
    return 2 * 60 * 1000;
  }

  /**
   * تحديث حالة التحميل
   */
  private setLoadingState(key: string, state: LoadingState): void {
    this.loadingStates.set(key, state);
  }

  /**
   * الحصول على حالة التحميل
   */
  getLoadingState(key: string): LoadingState {
    return this.loadingStates.get(key) || {
      isLoading: false,
      isRefreshing: false,
      error: null,
      lastFetch: null
    };
  }

  /**
   * إبطال التخزين المؤقت لنقطة نهاية معينة
   */
  invalidateCache(endpoint: string, queryParams?: ApiQueryParams): void {
    const cacheKey = CacheManager.createKey(endpoint, queryParams || {});
    const cache = this.selectCache(endpoint);
    cache.delete(cacheKey);
  }

  /**
   * مسح جميع التخزين المؤقت
   */
  clearAllCache(): void {
    globalCache.clear();
    staticDataCache.clear();
    dynamicDataCache.clear();
  }
}

// إنشاء مثيل عام لجلب البيانات
export const dataFetcher = new DataFetcher();

// --- دوال مساعدة لجلب البيانات المختلفة ---

/**
 * جلب الأجهزة مع دعم الترقيم والتصفية
 */
export const fetchDevices = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/devices-simple', queryParams, {
    cache: true,
    cacheTtl: 2 * 60 * 1000, // دقيقتان
    ...options
  });
};

/**
 * جلب المبيعات مع دعم الترقيم والتصفية
 */
export const fetchSales = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/sales', queryParams, {
    cache: true,
    cacheTtl: 2 * 60 * 1000,
    ...options
  });
};

/**
 * جلب أوامر التوريد مع دعم الترقيم والتصفية
 */
export const fetchSupplyOrders = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/supply', queryParams, {
    cache: true,
    cacheTtl: 2 * 60 * 1000,
    ...options
  });
};

/**
 * جلب المرتجعات مع دعم الترقيم والتصفية
 */
export const fetchReturns = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/returns', queryParams, {
    cache: true,
    cacheTtl: 2 * 60 * 1000,
    ...options
  });
};

/**
 * جلب العملاء مع دعم الترقيم والتصفية
 */
export const fetchClients = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/clients', queryParams, {
    cache: true,
    cacheTtl: 10 * 60 * 1000, // 10 دقائق
    ...options
  });
};

/**
 * جلب الموردين مع دعم الترقيم والتصفية
 */
export const fetchSuppliers = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/suppliers', queryParams, {
    cache: true,
    cacheTtl: 10 * 60 * 1000,
    ...options
  });
};

/**
 * جلب المخازن مع دعم الترقيم والتصفية
 */
export const fetchWarehouses = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/warehouses-simple', queryParams, {
    cache: true,
    cacheTtl: 30 * 60 * 1000, // 30 دقيقة
    ...options
  });
};

/**
 * جلب المستخدمين مع دعم الترقيم والتصفية
 */
export const fetchUsers = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/users', queryParams, {
    cache: true,
    cacheTtl: 30 * 60 * 1000,
    ...options
  });
};

/**
 * جلب الشركات المصنعة مع دعم الترقيم والتصفية
 */
export const fetchManufacturers = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/manufacturers', queryParams, {
    cache: true,
    cacheTtl: 30 * 60 * 1000, // 30 دقيقة
    ...options
  });
};

/**
 * جلب موديلات الأجهزة مع دعم الترقيم والتصفية
 */
export const fetchDeviceModels = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/device-models', queryParams, {
    cache: true,
    cacheTtl: 30 * 60 * 1000, // 30 دقيقة
    ...options
  });
};

/**
 * جلب أوامر التقييم مع دعم الترقيم والتصفية
 */
export const fetchEvaluationOrders = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/evaluations', queryParams, {
    cache: true,
    cacheTtl: 5 * 60 * 1000,
    ...options
  });
};

/**
 * جلب أوامر الصيانة مع دعم الترقيم والتصفية
 */
export const fetchMaintenanceOrders = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/maintenance-orders', queryParams, {
    cache: true,
    cacheTtl: 5 * 60 * 1000,
    ...options
  });
};

/**
 * جلب أوامر التسليم مع دعم الترقيم والتصفية
 */
export const fetchDeliveryOrders = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/delivery-orders', queryParams, {
    cache: true,
    cacheTtl: 5 * 60 * 1000,
    ...options
  });
};

/**
 * جلب الرسائل الداخلية مع دعم الترقيم والتصفية
 */
export const fetchInternalMessages = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/internal-messages', queryParams, {
    cache: true,
    cacheTtl: 1 * 60 * 1000, // دقيقة واحدة
    ...options
  });
};

/**
 * جلب طلبات الموظفين مع دعم الترقيم والتصفية
 */
export const fetchEmployeeRequests = (queryParams: ApiQueryParams = {}, options: DataFetchOptions = {}) => {
  return dataFetcher.fetchData('/api/employee-requests', queryParams, {
    cache: true,
    cacheTtl: 2 * 60 * 1000, // دقيقتان
    ...options
  });
};
