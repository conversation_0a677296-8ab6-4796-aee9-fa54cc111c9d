'use client';

import { useState, useEffect } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Smartphone, Plus, ArrowLeft } from 'lucide-react';
import type { Manufacturer, DeviceModel } from '@/lib/types';

interface ModelsListProps {
  manufacturer: Manufacturer | null;
  onBack: () => void;
  onAddModel: (manufacturer: Manufacturer) => void;
}

export default function ModelsList({ 
  manufacturer, 
  onBack, 
  onAddModel 
}: ModelsListProps) {
  const { 
    deviceModels, 
    fetchDeviceModelsData,
    isLoading 
  } = useStore();

  const [manufacturerModels, setManufacturerModels] = useState<DeviceModel[]>([]);

  useEffect(() => {
    if (manufacturer) {
      // تصفية الموديلات للشركة المحددة
      const models = deviceModels.filter(model => model.manufacturerId === manufacturer.id);
      setManufacturerModels(models);
    }
  }, [manufacturer, deviceModels]);

  if (!manufacturer) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <p className="text-muted-foreground">اختر شركة لعرض موديلاتها</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="p-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <CardTitle className="flex items-center gap-2">
              <Smartphone className="h-5 w-5" />
              موديلات {manufacturer.name}
            </CardTitle>
          </div>
          <Button
            onClick={() => onAddModel(manufacturer)}
            size="sm"
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            إضافة موديل جديد
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="text-muted-foreground mt-2">جاري تحميل الموديلات...</p>
          </div>
        ) : manufacturerModels.length === 0 ? (
          <div className="text-center py-8">
            <Smartphone className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <p className="text-muted-foreground mb-4">
              لا توجد موديلات لشركة {manufacturer.name}
            </p>
            <Button
              onClick={() => onAddModel(manufacturer)}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              إضافة أول موديل
            </Button>
          </div>
        ) : (
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم الموديل</TableHead>
                  <TableHead>الفئة</TableHead>
                  <TableHead>تاريخ الإضافة</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {manufacturerModels.map((model) => (
                  <TableRow key={model.id}>
                    <TableCell className="font-medium">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4 text-muted-foreground" />
                        {model.name}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {model.category || 'هاتف ذكي'}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-muted-foreground">
                      {model.createdAt ? new Date(model.createdAt).toLocaleDateString('ar-SA') : '-'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
        
        {manufacturerModels.length > 0 && (
          <div className="mt-4 text-center">
            <Badge variant="secondary">
              إجمالي الموديلات: {manufacturerModels.length}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
