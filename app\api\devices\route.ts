import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';
import {
  extractApiQueryParams,
  paginationToPrisma,
  sortToPrisma,
  searchToPrisma,
  filtersToPrisma,
  createPaginatedResponse,
  validatePaginationParams,
  validateSortParams
} from '@/lib/api-helpers';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استخراج معاملات API
    const allowedFilters = ['model', 'status', 'storage', 'condition', 'warehouseId', 'supplierId', 'priceMin', 'priceMax'];
    const queryParams = extractApiQueryParams(request, allowedFilters);

    // التحقق من صحة المعاملات
    if (queryParams.pagination) {
      const paginationErrors = validatePaginationParams(queryParams.pagination);
      if (paginationErrors.length > 0) {
        return NextResponse.json({ error: paginationErrors.join(', ') }, { status: 400 });
      }
    }

    const allowedSortFields = ['id', 'model', 'status', 'price', 'dateAdded', 'condition'];
    if (queryParams.sort) {
      const sortErrors = validateSortParams(queryParams.sort, allowedSortFields);
      if (sortErrors.length > 0) {
        return NextResponse.json({ error: sortErrors.join(', ') }, { status: 400 });
      }
    }

    // بناء شروط Prisma
    const paginationPrisma = paginationToPrisma(queryParams.pagination || {});
    const sortPrisma = sortToPrisma(queryParams.sort, allowedSortFields);
    const searchPrisma = searchToPrisma(queryParams.search, ['id', 'model', 'storage']);

    // معالجة الفلاتر المخصصة
    const filtersPrisma = filtersToPrisma(queryParams.filters || {});

    // معالجة فلاتر السعر
    if (queryParams.filters?.priceMin || queryParams.filters?.priceMax) {
      filtersPrisma.price = {};
      if (queryParams.filters.priceMin) {
        filtersPrisma.price.gte = Number(queryParams.filters.priceMin);
      }
      if (queryParams.filters.priceMax) {
        filtersPrisma.price.lte = Number(queryParams.filters.priceMax);
      }
      // إزالة فلاتر السعر من الفلاتر العامة
      delete filtersPrisma.priceMin;
      delete filtersPrisma.priceMax;
    }

    // دمج شروط البحث والتصفية
    const whereClause = {
      ...filtersPrisma,
      ...(searchPrisma && { ...searchPrisma })
    };

    // جلب العدد الإجمالي
    const total = await prisma.device.count({ where: whereClause });

    // جلب البيانات
    const devices = await prisma.device.findMany({
      where: whereClause,
      ...paginationPrisma,
      orderBy: sortPrisma || { id: 'desc' }
    });

    // إنشاء الاستجابة المرقمة
    const response = createPaginatedResponse(devices, total, queryParams.pagination || {}, queryParams);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Failed to fetch devices:', error);
    return NextResponse.json({ error: 'Failed to fetch devices' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newDevice = await request.json();

    // Basic validation
    if (!newDevice.id || !newDevice.model) {
      return NextResponse.json(
        { message: 'Device ID and model are required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if device already exists
      const existingDevice = await tx.device.findUnique({
        where: { id: newDevice.id }
      });

      if (existingDevice) {
        throw new Error('Device with this ID already exists');
      }

      // Create new device
      const device = await tx.device.create({
        data: {
          id: newDevice.id,
          model: newDevice.model,
          status: newDevice.status || 'متاح للبيع',
          storage: newDevice.storage || 'N/A',
          price: newDevice.price || 0,
          condition: newDevice.condition || 'جديد',
          warehouseId: newDevice.warehouseId,
          supplierId: newDevice.supplierId,
          dateAdded: new Date().toISOString(),
          replacementInfo: newDevice.replacementInfo
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created device: ${device.id} - ${device.model}`,
        tableName: 'device',
        recordId: device.id
      });

      return device;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create device:', error);

    if (error instanceof Error && error.message === 'Device with this ID already exists') {
      return NextResponse.json(
        { message: 'Device with this ID already exists' },
        { status: 409 }
    );
    }

    return NextResponse.json({ error: 'Failed to create device' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedDevice = await request.json();

    if (!updatedDevice.id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if device exists
      const existingDevice = await tx.device.findUnique({
        where: { id: updatedDevice.id }
      });

      if (!existingDevice) {
        throw new Error('Device not found');
      }

      // Update device
      const device = await tx.device.update({
        where: { id: updatedDevice.id },
        data: {
          model: updatedDevice.model || existingDevice.model,
          status: updatedDevice.status || existingDevice.status,
          storage: updatedDevice.storage || existingDevice.storage,
          price: updatedDevice.price !== undefined ? updatedDevice.price : existingDevice.price,
          condition: updatedDevice.condition || existingDevice.condition,
          warehouseId: updatedDevice.warehouseId,
          supplierId: updatedDevice.supplierId,
          replacementInfo: updatedDevice.replacementInfo
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated device: ${device.id} - ${device.model}`,
        tableName: 'device',
        recordId: device.id
      });

      return device;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update device:', error);

    if (error instanceof Error && error.message === 'Device not found') {
      return NextResponse.json({ message: 'Device not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update device' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Device ID is required' },
        { status: 400 }
    );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if device exists
      const existingDevice = await tx.device.findUnique({
        where: { id }
      });

      if (!existingDevice) {
        throw new Error('Device not found');
      }

      // فحص العلاقات قبل الحذف
      const relationCheck = await checkRelationsBeforeDelete(tx, 'device', id);

      if (relationCheck.hasRelations) {
        throw new Error(`Cannot delete device: ${relationCheck.relations.join(', ')}`);
      }

      // Delete device
      await tx.device.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted device: ${existingDevice.id} - ${existingDevice.model}`,
        tableName: 'device',
        recordId: id
      });

      return { message: 'Device deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete device:', error);

    if (error instanceof Error) {
      if (error.message === 'Device not found') {
        return NextResponse.json({ message: 'Device not found' }, { status: 404 });
      }
      if (error.message.startsWith('Cannot delete device:')) {
        const relations = error.message.replace('Cannot delete device: ', '');
        return NextResponse.json({
          error: 'Cannot delete device',
          reason: 'يوجد عمليات مرتبطة بهذا الجهاز',
          relations: relations.split(', ')
        }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete device' }, { status: 500 });
  }
}
