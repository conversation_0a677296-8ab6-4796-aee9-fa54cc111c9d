'use client';

import { useState, useMemo, useCallback } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import { PermissionGuard, ActionGuard } from '@/components/PermissionGuard';
import { usePermission } from '@/hooks/usePermission';
import type { Warehouse, WarehouseType } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Edit, Trash2, Package } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const initialFormState: Omit<Warehouse, 'id'> = {
  name: '',
  type: 'فرعي',
  location: '',
};

export default function WarehousesPage() {
  const { toast } = useToast();
  const { canCreate, canEdit, canDelete } = usePermission('warehouses');
  
  // ✅ استخدام Store API - البيانات متوفرة فوراً من الكاش
  const { 
    warehouses, 
    fetchWarehousesData, 
    addWarehouse: addWarehouseToStore,
    updateWarehouse: updateWarehouseInStore,
    deleteWarehouse: deleteWarehouseFromStore,
    isLoading 
  } = useStore();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(
    null
    );
  const [warehouseToDelete, setWarehouseToDelete] = useState<Warehouse | null>(
    null
    );
  const [formData, setFormData] =
    useState<Omit<Warehouse, 'id'>>(initialFormState);

  // ✅ دالة تحديث البيانات محسّنة مع useCallback
  const refreshWarehouses = useCallback(async () => {
    try {
      await fetchWarehousesData({ forceRefresh: true });
    } catch (error) {
      console.error('خطأ في تحميل المخازن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'فشل في تحميل المخازن'
      });
    }
  }, [fetchWarehousesData, toast]);

  // ✅ دالة إضافة محسّنة تستخدم Store API
  const addWarehouse = useCallback(async (warehouseData: any) => {
    try {
      const success = await addWarehouseToStore(warehouseData);
      if (success) {
        toast({
          title: 'تم بنجاح',
          description: 'تم إنشاء المخزن بنجاح'
        });
      }
      return success;
    } catch (error: any) {
      console.error('خطأ في إنشاء المخزن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message || 'فشل في إنشاء المخزن'
      });
      return false;
    }
  }, [addWarehouseToStore, toast]);

  // ✅ دالة تحديث محسّنة تستخدم Store API
  const updateWarehouse = useCallback(async (warehouseData: any) => {
    try {
      const success = await updateWarehouseInStore(warehouseData);
      if (success) {
        toast({
          title: 'تم بنجاح',
          description: 'تم تحديث المخزن بنجاح'
        });
      }
      return success;
    } catch (error: any) {
      console.error('خطأ في تحديث المخزن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message || 'فشل في تحديث المخزن'
      });
      return false;
    }
  }, [updateWarehouseInStore, toast]);

  // ✅ دالة حذف محسّنة تستخدم Store API
  const deleteWarehouse = useCallback(async (id: number) => {
    try {
      const success = await deleteWarehouseFromStore(id);
      if (success) {
        toast({
          title: 'تم بنجاح',
          description: 'تم حذف المخزن بنجاح'
        });
      }
      return success;
    } catch (error: any) {
      console.error('خطأ في حذف المخزن:', error);
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: error.message || 'فشل في حذف المخزن'
      });
      return false;
    }
  }, [deleteWarehouseFromStore, toast]);

  // Load data on component mount
  // ✅ إحصائيات ذكية مع useMemo
  const warehouseStats = useMemo(() => {
    return {
      total: warehouses.length,
      main: warehouses.filter(w => w.type === 'رئيسي').length,
      branch: warehouses.filter(w => w.type === 'فرعي').length
    };
  }, [warehouses]);

  const handleOpenDialog = (warehouse: Warehouse | null) => {
    if (warehouse) {
      setEditingWarehouse(warehouse);
      setFormData(warehouse);
    } else {
      setEditingWarehouse(null);
      setFormData(initialFormState);
    }
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingWarehouse(null);
    setFormData(initialFormState);
  };

  const handleSave = async () => {
    if (!formData.name || !formData.location) {
      toast({
        title: 'خطأ في الإدخال',
        description: 'يرجى ملء جميع الحقول.',
        variant: 'destructive',
      });
      return;
    }

    let success = false;
    if (editingWarehouse) {
      success = await updateWarehouse({ ...formData, id: editingWarehouse.id });
      if (success) {
        toast({
          title: 'تم التحديث',
          description: 'تم تحديث بيانات المخزن بنجاح.',
        });
      }
    } else {
      success = await addWarehouse(formData);
      if (success) {
        toast({ title: 'تمت الإضافة', description: 'تمت إضافة المخزن بنجاح.' });
      }
    }

    if (success) {
      handleDialogClose();
    }
  };

  const handleDelete = async () => {
    if (warehouseToDelete) {
      const success = await deleteWarehouse(warehouseToDelete.id);
      if (success) {
        toast({
          title: 'تم الحذف',
          description: `تم حذف مخزن ${warehouseToDelete.name} بنجاح.`,
        });
        setWarehouseToDelete(null);
      }
    }
  };

  return (
    <>
      {/* ✅ إحصائيات ذكية */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">إجمالي المخازن</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{warehouseStats.total}</div>
            <p className="text-xs text-muted-foreground">مخزن نشط</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المخازن الرئيسية</CardTitle>
            <Badge variant="default" className="h-4 w-4 p-0 text-xs">{warehouseStats.main}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{warehouseStats.main}</div>
            <p className="text-xs text-muted-foreground">مخزن رئيسي</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">المخازن الفرعية</CardTitle>
            <Badge variant="secondary" className="h-4 w-4 p-0 text-xs">{warehouseStats.branch}</Badge>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{warehouseStats.branch}</div>
            <p className="text-xs text-muted-foreground">مخزن فرعي</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>إدارة المخازن</CardTitle>
          <div className="flex gap-2">
            <Button 
              onClick={refreshWarehouses} 
              variant="outline" 
              size="sm"
              disabled={isLoading}
            >
              {isLoading ? 'جاري التحديث...' : 'تحديث البيانات'}
            </Button>
            <Button onClick={() => handleOpenDialog(null)}>
              إضافة مخزن جديد
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم المخزن</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>الموقع</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      جاري تحميل المخازن...
                    </TableCell>
                  </TableRow>
                ) : warehouses.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      لا توجد مخازن مسجلة
                    </TableCell>
                  </TableRow>
                ) : (
                  warehouses.map((warehouse) => (
                  <TableRow key={warehouse.id}>
                    <TableCell className="font-medium">
                      {warehouse.name}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          warehouse.type === 'رئيسي' ? 'default' : 'secondary'
                        }
                      >
                        {warehouse.type}
                      </Badge>
                    </TableCell>
                    <TableCell>{warehouse.location}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => handleOpenDialog(warehouse)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="text-destructive hover:text-destructive"
                          onClick={() => setWarehouseToDelete(warehouse)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={(e) => e.preventDefault()}
          onCloseAutoFocus={handleDialogClose}
        >
          <DialogHeader>
            <DialogTitle>
              {editingWarehouse ? 'تعديل مخزن' : 'إضافة مخزن جديد'}
            </DialogTitle>
            <DialogDescription>
              {editingWarehouse
                ? 'قم بتحديث بيانات المخزن.'
                : 'أدخل بيانات المخزن الجديد هنا. انقر على "حفظ" عند الانتهاء.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                placeholder="مثال: مخزن الفرع الشمالي"
                className="col-span-3"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                النوع
              </Label>
              <Select
                dir="rtl"
                value={formData.type}
                onValueChange={(value: WarehouseType) =>
                  setFormData({ ...formData, type: value })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="رئيسي">رئيسي</SelectItem>
                  <SelectItem value="فرعي">فرعي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                الموقع
              </Label>
              <Input
                id="location"
                placeholder="مثال: صنعاء - شارع تعز"
                className="col-span-3"
                value={formData.location}
                onChange={(e) =>
                  setFormData({ ...formData, location: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSave}>حفظ</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!warehouseToDelete}
        onOpenChange={() => setWarehouseToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">
              ⚠️ تأكيد حذف المخزن
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div className="text-gray-700">
                  هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف المخزن
                  <span className="font-semibold text-red-600">
                    {' "' + warehouseToDelete?.name + '"'}
                  </span>
                  بشكل دائم.
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-3">
                  <div className="text-yellow-800 text-sm">
                    <strong>تنبيه:</strong> سيتم فحص العلاقات المرتبطة قبل الحذف.
                    إذا كان للمخزن مبيعات، أوامر توريد، تحويلات مخزنية، أوامر تسليم، أو أجهزة، فلن يتم الحذف.
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-100 hover:bg-gray-200">
              إلغاء
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
