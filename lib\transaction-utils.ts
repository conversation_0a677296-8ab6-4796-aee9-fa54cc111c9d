import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';

/**
 * تنفيذ عملية داخل معاملة مع إدارة الأخطاء
 */
export async function executeInTransaction<T>(
  operation: (tx: Prisma.TransactionClient) => Promise<T>
): Promise<T> {
  return await prisma.$transaction(async (tx) => {
    return await operation(tx);
  });
}

/**
 * تنفيذ عدة عمليات داخل معاملة واحدة
 */
export async function executeMultipleInTransaction<T>(
  operations: ((tx: Prisma.TransactionClient) => Promise<any>)[]
): Promise<T[]> {
  return await prisma.$transaction(async (tx) => {
    const results: T[] = [];
    for (const operation of operations) {
      const result = await operation(tx);
      results.push(result);
    }
    return results;
  });
}

/**
 * إنشاء audit log داخل معاملة
 */
export async function createAuditLogInTransaction(
  tx: any,
  data: {
    userId: number;
    username: string;
    operation: string;
    details: string;
  }
) {
  return await tx.auditLog.create({
    data: {
      userId: data.userId,
      username: data.username,
      operation: data.operation,
      details: data.details,
      timestamp: new Date()
    }
  });
}

/**
 * التحقق من وجود العلاقات قبل الحذف
 */
export async function checkRelationsBeforeDelete(
  tx: Prisma.TransactionClient,
  tableName: string,
  recordId: string | number
): Promise<{ hasRelations: boolean; relations: string[] }> {
  const relations: string[] = [];
  
  try {
    switch (tableName) {
      case 'client':
        const clientOperations = await tx.maintenanceOrder.count({
          where: { clientId: recordId as string }
        });
        if (clientOperations > 0) {
          relations.push(`${clientOperations} maintenance orders`);
        }
        break;
        
      case 'device':
        const deviceLogs = await tx.maintenanceLog.count({
          where: { deviceId: recordId as string }
        });
        if (deviceLogs > 0) {
          relations.push(`${deviceLogs} maintenance logs`);
        }
        break;
        
      case 'supplier':
        const supplierOrders = await tx.maintenanceOrder.count({
          where: { supplierId: recordId as string }
        });
        if (supplierOrders > 0) {
          relations.push(`${supplierOrders} maintenance orders`);
        }
        break;
        
      default:
        // يمكن إضافة المزيد من الجداول حسب الحاجة
        break;
    }
    
    return {
      hasRelations: relations.length > 0,
      relations
    };
  } catch (error) {
    console.error('Error checking relations:', error);
    return {
      hasRelations: false,
      relations: []
    };
  }
}

/**
 * إنشاء رقم تسلسلي فريد داخل معاملة
 */
export async function generateUniqueId(
  tx: Prisma.TransactionClient,
  tableName: string,
  prefix: string = ''
): Promise<string> {
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    let id: string;
    let exists = false;

    try {
      switch (tableName) {
        case 'supplyOrder':
          // للتوريد، استخدم أرقام متسلسلة
          const lastSupplyOrder = await tx.supplyOrder.findFirst({
            where: {
              supplyOrderId: {
                startsWith: prefix
              }
            },
            orderBy: {
              supplyOrderId: 'desc'
            }
          });

          let nextNumber = 1;
          if (lastSupplyOrder && lastSupplyOrder.supplyOrderId) {
            const currentNumber = parseInt(lastSupplyOrder.supplyOrderId.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextNumber}`;

          // التحقق من عدم وجود الرقم
          const existingSupplyOrder = await tx.supplyOrder.findUnique({
            where: { supplyOrderId: id }
          });
          exists = !!existingSupplyOrder;
          break;

        case 'sale':
          // للمبيعات، استخدم أرقام متسلسلة
          const lastSale = await tx.sale.findFirst({
            where: {
              soNumber: {
                startsWith: prefix
              }
            },
            orderBy: {
              soNumber: 'desc'
            }
          });

          let nextSaleNumber = 1;
          if (lastSale && lastSale.soNumber) {
            const currentNumber = parseInt(lastSale.soNumber.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextSaleNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextSaleNumber}`;

          // التحقق من عدم وجود الرقم
          const existingSale = await tx.sale.findUnique({
            where: { soNumber: id }
          });
          exists = !!existingSale;
          break;

        case 'return':
          // للمرتجعات، استخدم أرقام متسلسلة
          const lastReturn = await tx.return.findFirst({
            where: {
              roNumber: {
                startsWith: prefix
              }
            },
            orderBy: {
              roNumber: 'desc'
            }
          });

          let nextReturnNumber = 1;
          if (lastReturn && lastReturn.roNumber) {
            const currentNumber = parseInt(lastReturn.roNumber.replace(prefix, ''));
            if (!isNaN(currentNumber)) {
              nextReturnNumber = currentNumber + 1;
            }
          }

          id = `${prefix}${nextReturnNumber}`;

          // التحقق من عدم وجود الرقم
          const existingReturn = await tx.return.findUnique({
            where: { roNumber: id }
          });
          exists = !!existingReturn;
          break;

        case 'maintenanceOrder':
          // للصيانة، استخدم timestamp + random
          const timestamp = Date.now();
          const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${timestamp}${random}`;

          const order = await tx.maintenanceOrder.findFirst({
            where: { orderNumber: id }
          });
          exists = !!order;
          break;

        case 'deliveryOrder':
          // للتسليم، استخدم timestamp + random
          const deliveryTimestamp = Date.now();
          const deliveryRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${deliveryTimestamp}${deliveryRandom}`;

          const delivery = await tx.deliveryOrder.findFirst({
            where: { deliveryOrderNumber: id }
          });
          exists = !!delivery;
          break;

        default:
          // للجداول الأخرى، استخدم timestamp + random
          const defaultTimestamp = Date.now();
          const defaultRandom = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
          id = `${prefix}${defaultTimestamp}${defaultRandom}`;
          exists = false;
      }

      if (!exists) {
        return id;
      }
    } catch (error) {
      console.error('Error checking ID uniqueness:', error);
    }

    attempts++;
    // انتظار قصير قبل المحاولة التالية
    await new Promise(resolve => setTimeout(resolve, 10));
  }

  throw new Error(`Failed to generate unique ID after ${maxAttempts} attempts`);
}
