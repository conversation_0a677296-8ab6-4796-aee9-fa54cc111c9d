import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET - جلب جميع الأجهزة (بدون authentication للاختبار)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '1000');
    const search = searchParams.get('search') || '';
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where = search ? {
      OR: [
        { id: { contains: search, mode: 'insensitive' as const } },
        { model: { contains: search, mode: 'insensitive' as const } },
        { storage: { contains: search, mode: 'insensitive' as const } },
        { status: { contains: search, mode: 'insensitive' as const } }
      ]
    } : {};

    const [devices, total] = await Promise.all([
      prisma.device.findMany({
        where,
        orderBy: { dateAdded: 'desc' },
        skip,
        take: limit
      }),
      prisma.device.count({ where })
    ]);

    const hasMore = skip + devices.length < total;

    return NextResponse.json({
      data: devices,
      total,
      hasMore,
      page,
      limit
    });
  } catch (error) {
    console.error('خطأ في جلب الأجهزة:', error);
    return NextResponse.json(
      { error: 'فشل في جلب الأجهزة' },
      { status: 500 }
    );
  }
}

// POST - إنشاء جهاز جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, model, status, storage, price, condition, warehouseId, supplierId } = body;

    // التحقق من البيانات المطلوبة
    if (!id || !model) {
      return NextResponse.json(
        { error: 'رقم الجهاز والموديل مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من عدم تكرار رقم الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (existingDevice) {
      return NextResponse.json(
        { error: 'يوجد جهاز بهذا الرقم بالفعل' },
        { status: 400 }
      );
    }

    const device = await prisma.device.create({
      data: {
        id,
        model,
        status: status || 'متاح للبيع',
        storage: storage || 'N/A',
        price: price || 0,
        condition: condition || 'جديد',
        warehouseId: warehouseId || null,
        supplierId: supplierId || null,
        dateAdded: new Date().toISOString()
      }
    });

    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('خطأ في إنشاء الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في إنشاء الجهاز' },
      { status: 500 }
    );
  }
}

// PUT - تحديث جهاز موجود
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, model, status, storage, price, condition, warehouseId, supplierId } = body;

    // التحقق من البيانات المطلوبة
    if (!id) {
      return NextResponse.json(
        { error: 'رقم الجهاز مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'الجهاز غير موجود' },
        { status: 404 }
      );
    }

    const device = await prisma.device.update({
      where: { id },
      data: {
        model: model || existingDevice.model,
        status: status || existingDevice.status,
        storage: storage || existingDevice.storage,
        price: price !== undefined ? price : existingDevice.price,
        condition: condition || existingDevice.condition,
        warehouseId: warehouseId !== undefined ? warehouseId : existingDevice.warehouseId,
        supplierId: supplierId !== undefined ? supplierId : existingDevice.supplierId
      }
    });

    return NextResponse.json(device);
  } catch (error) {
    console.error('خطأ في تحديث الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في تحديث الجهاز' },
      { status: 500 }
    );
  }
}

// DELETE - حذف جهاز
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'رقم الجهاز مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من وجود الجهاز
    const existingDevice = await prisma.device.findUnique({
      where: { id }
    });

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'الجهاز غير موجود' },
        { status: 404 }
      );
    }

    // 🔒 فحص العلاقات المرتبطة قبل الحذف
    const relatedRecords = {
      sales: 0,
      supply: 0,
      maintenance: 0,
      returns: 0,
      evaluations: 0
    };

    // فحص المبيعات المرتبطة
    try {
      const salesWithDevice = await prisma.sale.findMany({
        where: {
          OR: [
            { items: { path: '$[*].deviceId', equals: id } },
            { items: { path: '$[*].imei', equals: id } }
          ]
        },
        select: { id: true, soNumber: true }
      });
      relatedRecords.sales = salesWithDevice.length;
    } catch (error) {
      console.warn('Error checking sales:', error);
    }

    // فحص أوامر التوريد المرتبطة
    try {
      const supplyWithDevice = await prisma.supplyOrder.findMany({
        where: {
          OR: [
            { items: { path: '$[*].imei', equals: id } },
            { items: { path: '$[*].deviceId', equals: id } }
          ]
        },
        select: { id: true, supplyOrderId: true }
      });
      relatedRecords.supply = supplyWithDevice.length;
    } catch (error) {
      console.warn('Error checking supply orders:', error);
    }

    // فحص أوامر الصيانة المرتبطة
    try {
      const maintenanceWithDevice = await prisma.maintenanceOrder.findMany({
        where: {
          OR: [
            { items: { path: '$[*].deviceId', equals: id } },
            { items: { path: '$[*].imei', equals: id } }
          ]
        },
        select: { id: true, orderNumber: true }
      });
      relatedRecords.maintenance = maintenanceWithDevice.length;
    } catch (error) {
      console.warn('Error checking maintenance orders:', error);
    }

    // فحص عمليات الإرجاع المرتبطة
    try {
      const returnsWithDevice = await prisma.return.findMany({
        where: {
          OR: [
            { items: { path: '$[*].deviceId', equals: id } },
            { items: { path: '$[*].replacementDeviceId', equals: id } }
          ]
        },
        select: { id: true, returnNumber: true }
      });
      relatedRecords.returns = returnsWithDevice.length;
    } catch (error) {
      console.warn('Error checking returns:', error);
    }

    // فحص أوامر التقييم المرتبطة
    try {
      const evaluationsWithDevice = await prisma.evaluationOrder.findMany({
        where: {
          OR: [
            { items: { path: '$[*].deviceId', equals: id } }
          ]
        },
        select: { id: true, evaluationOrderNumber: true }
      });
      relatedRecords.evaluations = evaluationsWithDevice.length;
    } catch (error) {
      console.warn('Error checking evaluations:', error);
    }

    // التحقق من وجود علاقات مرتبطة
    const totalRelated = Object.values(relatedRecords).reduce((sum, count) => sum + count, 0);

    if (totalRelated > 0) {
      const relatedMessages = [];
      if (relatedRecords.sales > 0) relatedMessages.push(`${relatedRecords.sales} عملية بيع`);
      if (relatedRecords.supply > 0) relatedMessages.push(`${relatedRecords.supply} أمر توريد`);
      if (relatedRecords.maintenance > 0) relatedMessages.push(`${relatedRecords.maintenance} أمر صيانة`);
      if (relatedRecords.returns > 0) relatedMessages.push(`${relatedRecords.returns} عملية إرجاع`);
      if (relatedRecords.evaluations > 0) relatedMessages.push(`${relatedRecords.evaluations} أمر تقييم`);

      return NextResponse.json({
        error: 'لا يمكن حذف الجهاز',
        reason: 'الجهاز مرتبط بعمليات أخرى في النظام',
        relatedRecords,
        details: `الجهاز مرتبط بـ: ${relatedMessages.join('، ')}`,
        suggestion: 'يمكنك تغيير حالة الجهاز إلى "غير متاح" بدلاً من حذفه',
        deviceInfo: {
          id: existingDevice.id,
          model: existingDevice.model,
          status: existingDevice.status
        }
      }, { status: 400 });
    }

    // إذا لم توجد علاقات مرتبطة، يمكن الحذف بأمان
    await prisma.device.delete({
      where: { id }
    });

    return NextResponse.json({ 
      message: 'تم حذف الجهاز بنجاح',
      deletedDevice: {
        id: existingDevice.id,
        model: existingDevice.model
      }
    });
  } catch (error) {
    console.error('خطأ في حذف الجهاز:', error);
    return NextResponse.json(
      { error: 'فشل في حذف الجهاز' },
      { status: 500 }
    );
  }
}
